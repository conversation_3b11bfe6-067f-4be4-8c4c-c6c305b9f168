# LOBO AI Complete Setup Guide

This guide will help you set up the complete LOBO AI system with backend podcast generation.

## Prerequisites

- Node.js 18+ and npm
- Python 3.8+
- Firebase project with Realtime Database and Storage
- API keys for OpenAI and ElevenLabs

## Step 1: Frontend Setup

### 1.1 Install Frontend Dependencies

```bash
npm install
```

### 1.2 Configure Frontend Environment

```bash
cp .env.example .env
```

Edit `.env` with your Firebase configuration and backend URL:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=loboai.firebaseapp.com
VITE_FIREBASE_DATABASE_URL=https://loboai-default-rtdb.firebaseio.com/
VITE_FIREBASE_PROJECT_ID=loboai
VITE_FIREBASE_STORAGE_BUCKET=loboai.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Backend API Configuration
VITE_BACKEND_URL=http://localhost:8000
```

## Step 2: Backend Setup

### 2.1 Install Backend Dependencies

```bash
cd python_backend
pip install -r requirements.txt
```

### 2.2 Configure Backend Environment

```bash
cp .env.example .env
```

Edit `python_backend/.env` with your API keys:

```env
# Required API Keys
OPENAI_API_KEY=your_openai_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Optional (for enhanced stock data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# Firebase Configuration
FIREBASE_PROJECT_ID=loboai
FIREBASE_STORAGE_BUCKET=loboai.firebasestorage.app
FIREBASE_DATABASE_URL=https://loboai-default-rtdb.firebaseio.com/

# Server Configuration
DEBUG=True
HOST=0.0.0.0
PORT=8000
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
```

### 2.3 Firebase Service Account

Ensure the Firebase service account key file (`loboai-firebase-adminsdk-fbsvc-e126d3a1f8.json`) is in the project root directory.

## Step 3: Getting API Keys

### 3.1 OpenAI API Key

1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your backend `.env` file

### 3.2 ElevenLabs API Key

1. Go to [ElevenLabs](https://elevenlabs.io/)
2. Sign up and go to your profile
3. Copy your API key
4. Add it to your backend `.env` file

### 3.3 Stock Data APIs (Optional)

**Alpha Vantage:**
1. Go to [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
2. Get a free API key
3. Add to backend `.env`

**Finnhub:**
1. Go to [Finnhub](https://finnhub.io/)
2. Sign up for free API key
3. Add to backend `.env`

## Step 4: Firebase Setup

### 4.1 Firebase Storage Rules

Update your Firebase Storage rules to allow authenticated uploads:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /podcasts/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 4.2 Firebase Realtime Database Rules

Update your Realtime Database rules:

```json
{
  "rules": {
    "users": {
      "$uid": {
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    }
  }
}
```

## Step 5: Running the Application

### 5.1 Start the Backend

```bash
cd python_backend
python main.py
```

The backend will be available at http://localhost:8000

### 5.2 Start the Frontend

In a new terminal:

```bash
npm run dev
```

The frontend will be available at http://localhost:5173

## Step 6: Testing the Integration

### 6.1 Complete User Profile

1. Open http://localhost:5173
2. Sign in with Google
3. Complete your profile with:
   - Selected stocks (e.g., AAPL, GOOGL, TSLA)
   - Complexity level
   - Duration preference
   - Tone preference

### 6.2 Generate Your First Podcast

1. Go to the Dashboard
2. Click the "Generate Now" button
3. Wait for the generation process to complete
4. Your podcast will appear in the list and be ready to play

## Step 7: Verification

### 7.1 Check Backend Health

Visit http://localhost:8000/health to verify all services are working:

```json
{
  "status": "ok",
  "timestamp": "2024-01-01T12:00:00",
  "services": {
    "openai": "ok",
    "elevenlabs": "ok",
    "firebase": "ok",
    "stock_service": "ok"
  }
}
```

### 7.2 Check API Documentation

Visit http://localhost:8000/docs for interactive API documentation.

## Troubleshooting

### Common Issues

1. **CORS Errors**: Make sure `ALLOWED_ORIGINS` in backend includes your frontend URL
2. **API Key Errors**: Verify all required API keys are set correctly
3. **Firebase Errors**: Check service account file path and Firebase rules
4. **Import Errors**: Ensure all Python dependencies are installed

### Debug Mode

Set `DEBUG=True` in backend `.env` for detailed logging.

### Logs

Check console output for both frontend and backend for error messages.

## Production Deployment

For production deployment:

1. Set `DEBUG=False` in backend
2. Update `ALLOWED_ORIGINS` with your production domain
3. Use environment variables for all secrets
4. Set up proper logging and monitoring
5. Use a production WSGI server like Gunicorn

## Support

- Check the backend logs for detailed error information
- Verify all API keys are valid and have sufficient credits
- Ensure Firebase project has proper permissions set up
