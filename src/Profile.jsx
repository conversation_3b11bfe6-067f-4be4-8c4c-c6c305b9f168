import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { auth, saveUserProfile, getUserProfile } from './firebase_util';
import { useAuthState } from 'react-firebase-hooks/auth';
import { Search, X, Clock, Volume2, Brain, Calendar, Sparkles, TrendingUp } from 'lucide-react';
import { POPULAR_STOCKS, getStocksBySector, getPopularStocks, searchStocks, getAllSectors } from './data/stocks';
import { darkTheme, cssVariables } from './styles/theme';
import StockCard from './components/StockCard';
import StockLogo from './components/StockLogo';

const Profile = () => {
  const [user, loading] = useAuthState(auth);
  const navigate = useNavigate();

  // Helper function to get user's timezone safely
  const getUserTimezone = () => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone || 'America/New_York';
    } catch (error) {
      console.warn('Could not detect user timezone, using default:', error);
      return 'America/New_York';
    }
  };

  // Helper function to validate timezone
  const isValidTimezone = (timezone) => {
    try {
      new Date().toLocaleTimeString('en-US', { timeZone: timezone });
      return true;
    } catch (error) {
      return false;
    }
  };

  const [formData, setFormData] = useState({
    selectedStocks: [],
    complexity: 'intermediate',
    duration: '10',
    tone: 'casual',
    deliveryTime: '09:00',
    timezone: getUserTimezone() // Auto-detect user's timezone with fallback
  });

  const [stockSearch, setStockSearch] = useState('');
  const [selectedSector, setSelectedSector] = useState('all');
  const [viewMode, setViewMode] = useState('popular'); // 'popular', 'all', 'sectors'
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login');
    }
  }, [user, loading, navigate]);

  useEffect(() => {
    // Load existing profile if available
    const loadProfile = async () => {
      if (user) {
        const result = await getUserProfile(user.uid);
        if (result.success && result.data) {
          // Ensure timezone is set and valid for existing users
          let timezone = result.data.timezone || getUserTimezone();

          // Validate and fix common timezone issues
          if (timezone === 'Asia/Mumbai') {
            timezone = 'Asia/Kolkata'; // Fix old Mumbai timezone
          } else if (timezone === 'America/Buenos_Aires') {
            timezone = 'America/Argentina/Buenos_Aires'; // Fix Buenos Aires timezone
          }

          // Final validation
          if (!isValidTimezone(timezone)) {
            console.warn(`Invalid timezone '${timezone}' detected, using default`);
            timezone = getUserTimezone();
          }

          const profileData = {
            ...result.data,
            timezone: timezone
          };
          setFormData(profileData);
        }
      }
    };
    loadProfile();
  }, [user]);

  const addStock = (stock) => {
    const stockSymbol = typeof stock === 'string' ? stock : stock.symbol;
    if (!formData.selectedStocks.includes(stockSymbol)) {
      setFormData(prev => ({
        ...prev,
        selectedStocks: [...prev.selectedStocks, stockSymbol]
      }));
    }
    setStockSearch('');
  };

  const removeStock = (stockSymbol) => {
    setFormData(prev => ({
      ...prev,
      selectedStocks: prev.selectedStocks.filter(s => s !== stockSymbol)
    }));
  };

  const toggleStock = (stock) => {
    const stockSymbol = typeof stock === 'string' ? stock : stock.symbol;
    if (formData.selectedStocks.includes(stockSymbol)) {
      removeStock(stockSymbol);
    } else {
      addStock(stock);
    }
  };

  const handleSave = async () => {
    if (formData.selectedStocks.length === 0) {
      setError('Please select at least one stock to follow');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await saveUserProfile(user.uid, formData);
      if (result.success) {
        navigate('/dashboard');
      } else {
        setError(result.error || 'Failed to save profile');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Get filtered stocks based on search and view mode
  const getFilteredStocks = () => {
    let stocks = [];

    if (stockSearch.trim()) {
      // If searching, search all stocks
      stocks = searchStocks(stockSearch);
    } else {
      // Otherwise, filter by view mode
      switch (viewMode) {
        case 'popular':
          stocks = getPopularStocks();
          break;
        case 'sectors':
          stocks = selectedSector === 'all' ? POPULAR_STOCKS : getStocksBySector(selectedSector);
          break;
        default:
          stocks = POPULAR_STOCKS;
      }
    }

    // Filter out already selected stocks
    return stocks.filter(stock => !formData.selectedStocks.includes(stock.symbol));
  };

  const filteredStocks = getFilteredStocks();

  if (loading) {
    return (
      <div
        className="min-h-screen flex items-center justify-center"
        style={{ background: darkTheme.gradients.primary }}
      >
        <div className="relative">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2"
            style={{ borderColor: darkTheme.colors.accent.primary }}
          ></div>
          <div
            className="absolute inset-0 animate-ping rounded-full h-12 w-12 border-2 opacity-20"
            style={{ borderColor: darkTheme.colors.accent.primary }}
          ></div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Inject CSS Variables */}
      <style dangerouslySetInnerHTML={{ __html: cssVariables }} />

      <div
        className="min-h-screen py-8 px-4"
        style={{ background: darkTheme.gradients.primary }}
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8 sm:mb-12 animate-fadeInUp">
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-3 mb-4 sm:mb-6">
              <div
                className="p-3 rounded-xl animate-glow"
                style={{
                  background: darkTheme.gradients.accent,
                  boxShadow: darkTheme.shadows.glow
                }}
              >
                <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
              </div>
              <h1
                className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent text-center sm:text-left"
                style={{ fontFamily: darkTheme.typography.fontFamily.primary }}
              >
                Customize Your Portfolio
              </h1>
            </div>
            <p
              className="text-base sm:text-lg lg:text-xl max-w-2xl mx-auto px-4"
              style={{ color: darkTheme.colors.text.secondary }}
            >
              Select your favorite stocks and preferences to create the perfect daily market update
            </p>
          </div>

          {/* Main Content Container */}
          <div
            className="rounded-2xl border backdrop-blur-sm p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8 lg:space-y-10 animate-fadeInScale"
            style={{
              background: darkTheme.gradients.card,
              borderColor: darkTheme.colors.border.primary,
              boxShadow: darkTheme.shadows.xl
            }}
          >
            {/* Stock Selection */}
            <div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
                <div className="flex items-center space-x-3">
                  <div
                    className="p-2 rounded-lg"
                    style={{ backgroundColor: `${darkTheme.colors.accent.primary}20` }}
                  >
                    <Search
                      className="w-5 h-5 sm:w-6 sm:h-6"
                      style={{ color: darkTheme.colors.accent.primary }}
                    />
                  </div>
                  <h2
                    className="text-xl sm:text-2xl font-bold"
                    style={{ color: darkTheme.colors.text.primary }}
                  >
                    Select Your Stocks
                  </h2>
                </div>

                {/* View Mode Toggle */}
                <div className="flex space-x-1 sm:space-x-2 overflow-x-auto">
                  {['popular', 'all', 'sectors'].map((mode) => (
                    <button
                      key={mode}
                      onClick={() => setViewMode(mode)}
                      className={`px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                        viewMode === mode
                          ? 'text-white shadow-lg'
                          : 'hover:opacity-80'
                      }`}
                      style={{
                        background: viewMode === mode
                          ? darkTheme.gradients.accent
                          : `${darkTheme.colors.background.secondary}80`,
                        color: viewMode === mode
                          ? 'white'
                          : darkTheme.colors.text.secondary,
                        border: `1px solid ${viewMode === mode
                          ? darkTheme.colors.accent.primary
                          : darkTheme.colors.border.secondary}`
                      }}
                    >
                      {mode === 'popular' && <Sparkles className="w-3 h-3 sm:w-4 sm:h-4 inline mr-1" />}
                      {mode.charAt(0).toUpperCase() + mode.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              {/* Selected Stocks */}
              {formData.selectedStocks.length > 0 && (
                <div className="mb-6">
                  <h3
                    className="text-lg font-semibold mb-3"
                    style={{ color: darkTheme.colors.text.primary }}
                  >
                    Selected Stocks ({formData.selectedStocks.length})
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {formData.selectedStocks.map(stockSymbol => {
                      const stockData = POPULAR_STOCKS.find(s => s.symbol === stockSymbol);
                      const stock = stockData || { symbol: stockSymbol, name: stockSymbol, sector: 'Unknown', popular: false };
                      const sectorColor = darkTheme.colors.sectors[stock.sector?.toLowerCase().replace(' ', '')] || darkTheme.colors.accent.primary;

                      return (
                        <div
                          key={stockSymbol}
                          className="flex items-center space-x-3 px-4 py-3 rounded-xl border-2 transition-all duration-200 hover:scale-105 group"
                          style={{
                            background: `linear-gradient(135deg, ${sectorColor}15 0%, ${sectorColor}25 100%)`,
                            borderColor: sectorColor,
                            boxShadow: `0 4px 12px ${sectorColor}20`
                          }}
                        >
                          {/* Stock Logo */}
                          <div className="flex-shrink-0 relative">
                            <StockLogo
                              ticker={stock.symbol}
                              size={32}
                              className="shadow-lg"
                              isCustomStock={stock.sector === 'Custom'}
                            />
                            {/* Selection indicator */}
                            <div
                              className="absolute -top-1 -right-1 w-3 h-3 rounded-full border border-white"
                              style={{ backgroundColor: sectorColor }}
                            >
                              <div className="absolute inset-0 rounded-full animate-ping" style={{ backgroundColor: sectorColor, opacity: 0.4 }}></div>
                            </div>
                          </div>

                          {/* Stock Info */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h4
                                className="font-bold text-sm text-white truncate"
                                style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)' }}
                              >
                                {stock.symbol}
                              </h4>
                              {stock.popular && (
                                <span className="text-xs">⭐</span>
                              )}
                            </div>
                            <p
                              className="text-xs truncate"
                              style={{ color: darkTheme.colors.text.secondary }}
                              title={stock.name}
                            >
                              {stock.name}
                            </p>
                          </div>

                          {/* Remove Button */}
                          <button
                            onClick={() => removeStock(stockSymbol)}
                            className="p-1.5 rounded-full transition-all duration-200 hover:bg-red-500/20 hover:scale-110 opacity-70 group-hover:opacity-100"
                            style={{ color: darkTheme.colors.accent.error }}
                            title="Remove stock"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Stock Search */}
              <div className="relative mb-6">
                <div className="relative">
                  <Search
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5"
                    style={{ color: darkTheme.colors.text.tertiary }}
                  />
                  <input
                    type="text"
                    placeholder="Search stocks by symbol or company name..."
                    value={stockSearch}
                    onChange={(e) => setStockSearch(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && stockSearch.trim()) {
                        const searchTerm = stockSearch.trim().toUpperCase();
                        // Check if it's already in the list or selected
                        const existsInList = POPULAR_STOCKS.some(stock => 
                          stock.symbol.toUpperCase() === searchTerm
                        );
                        const alreadySelected = formData.selectedStocks.includes(searchTerm);
                        
                        if (!existsInList && !alreadySelected) {
                          // Add as custom stock
                          addStock({
                            symbol: searchTerm,
                            name: searchTerm,
                            sector: 'Custom',
                            popular: false
                          });
                        } else if (existsInList && !alreadySelected) {
                          // Add existing stock
                          const stock = POPULAR_STOCKS.find(s => s.symbol.toUpperCase() === searchTerm);
                          addStock(stock);
                        }
                      }
                    }}
                    className="w-full pl-12 pr-4 py-4 rounded-xl border-2 transition-all duration-200 focus:outline-none text-lg"
                    style={{
                      background: darkTheme.colors.background.secondary,
                      borderColor: stockSearch ? darkTheme.colors.accent.primary : darkTheme.colors.border.secondary,
                      color: darkTheme.colors.text.primary,
                      boxShadow: stockSearch ? darkTheme.shadows.glow : 'none'
                    }}
                  />
                  {stockSearch && (
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                      {/* Add Custom Stock Button */}
                      {stockSearch.trim() && 
                       !POPULAR_STOCKS.some(stock => 
                         stock.symbol.toLowerCase().includes(stockSearch.toLowerCase()) ||
                         stock.name.toLowerCase().includes(stockSearch.toLowerCase())
                       ) && 
                       !formData.selectedStocks.includes(stockSearch.trim().toUpperCase()) && (
                        <button
                          onClick={() => {
                            const customStock = {
                              symbol: stockSearch.trim().toUpperCase(),
                              name: stockSearch.trim().toUpperCase(),
                              sector: 'Custom',
                              popular: false
                            };
                            addStock(customStock);
                          }}
                          className="px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200 hover:scale-105"
                          style={{
                            background: darkTheme.colors.accent.primary,
                            color: 'white'
                          }}
                          title={`Add ${stockSearch.trim().toUpperCase()} as custom stock`}
                        >
                          + Add
                        </button>
                      )}
                      <button
                        onClick={() => setStockSearch('')}
                        className="p-1 rounded-full transition-colors duration-200 hover:bg-gray-600"
                      >
                        <X
                          className="w-5 h-5"
                          style={{ color: darkTheme.colors.text.tertiary }}
                        />
                      </button>
                    </div>
                  )}
                </div>
                
                {/* Search hint */}
                {stockSearch.trim() && 
                 !POPULAR_STOCKS.some(stock => 
                   stock.symbol.toLowerCase().includes(stockSearch.toLowerCase()) ||
                   stock.name.toLowerCase().includes(stockSearch.toLowerCase())
                 ) && 
                 !formData.selectedStocks.includes(stockSearch.trim().toUpperCase()) && (
                  <div className="mt-2 text-sm" style={{ color: darkTheme.colors.text.tertiary }}>
                    Press Enter or click "+ Add" to add "{stockSearch.trim().toUpperCase()}" as a custom stock
                  </div>
                )}
              </div>

              {/* Sector Filter (when in sectors mode) */}
              {viewMode === 'sectors' && (
                <div className="mb-6">
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setSelectedSector('all')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        selectedSector === 'all' ? 'text-white' : ''
                      }`}
                      style={{
                        background: selectedSector === 'all'
                          ? darkTheme.gradients.accent
                          : darkTheme.colors.background.secondary,
                        color: selectedSector === 'all'
                          ? 'white'
                          : darkTheme.colors.text.secondary,
                        border: `1px solid ${selectedSector === 'all'
                          ? darkTheme.colors.accent.primary
                          : darkTheme.colors.border.secondary}`
                      }}
                    >
                      All Sectors
                    </button>
                    {getAllSectors().map((sector) => (
                      <button
                        key={sector}
                        onClick={() => setSelectedSector(sector)}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                          selectedSector === sector ? 'text-white' : ''
                        }`}
                        style={{
                          background: selectedSector === sector
                            ? darkTheme.gradients.accent
                            : darkTheme.colors.background.secondary,
                          color: selectedSector === sector
                            ? 'white'
                            : darkTheme.colors.text.secondary,
                          border: `1px solid ${selectedSector === sector
                            ? darkTheme.colors.accent.primary
                            : darkTheme.colors.border.secondary}`
                        }}
                      >
                        {sector}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Stock Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4">
                {filteredStocks.slice(0, 30).map((stock, index) => (
                  <div
                    key={stock.symbol}
                    className="animate-fadeInUp"
                    style={{ animationDelay: `${index * 0.05}s` }}
                  >
                    <StockCard
                      stock={stock}
                      isSelected={formData.selectedStocks.includes(stock.symbol)}
                      onClick={toggleStock}
                      size="medium"
                      showSector={true}
                      className="hover-lift h-full"
                    />
                  </div>
                ))}
              </div>

              {/* Load More Button */}
              {filteredStocks.length > 30 && (
                <div className="text-center mt-6">
                  <button
                    onClick={() => {
                      // This would expand the grid - for now just show a message
                      alert(`${filteredStocks.length - 30} more stocks available. Use search to find specific stocks.`);
                    }}
                    className="px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105"
                    style={{
                      background: darkTheme.colors.background.secondary,
                      color: darkTheme.colors.text.secondary,
                      border: `1px solid ${darkTheme.colors.border.secondary}`
                    }}
                  >
                    View {filteredStocks.length - 30} More Stocks
                  </button>
                </div>
              )}

              {filteredStocks.length === 0 && stockSearch && (
                <div
                  className="text-center py-12"
                  style={{ color: darkTheme.colors.text.tertiary }}
                >
                  <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg">No stocks found matching "{stockSearch}"</p>
                  <p className="text-sm mt-2">Try searching by ticker symbol or company name</p>
                </div>
              )}
          </div>

            {/* Complexity Level */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${darkTheme.colors.accent.success}20` }}
                >
                  <Brain
                    className="w-6 h-6"
                    style={{ color: darkTheme.colors.accent.success }}
                  />
                </div>
                <h2
                  className="text-2xl font-bold"
                  style={{ color: darkTheme.colors.text.primary }}
                >
                  Complexity Level
                </h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { value: 'beginner', label: 'Beginner', desc: 'Explained like you\'re 5', icon: '🎓' },
                  { value: 'intermediate', label: 'Intermediate', desc: 'Casual analyst tone', icon: '📊' },
                  { value: 'advanced', label: 'Advanced', desc: 'Professional insights', icon: '🧠' }
                ].map(option => (
                  <button
                    key={option.value}
                    onClick={() => setFormData(prev => ({ ...prev, complexity: option.value }))}
                    className={`p-6 rounded-xl border-2 text-left transition-all duration-300 hover:scale-105 ${
                      formData.complexity === option.value ? 'shadow-lg' : ''
                    }`}
                    style={{
                      background: formData.complexity === option.value
                        ? `linear-gradient(135deg, ${darkTheme.colors.accent.success}20 0%, ${darkTheme.colors.accent.success}10 100%)`
                        : darkTheme.gradients.stockCard,
                      borderColor: formData.complexity === option.value
                        ? darkTheme.colors.accent.success
                        : darkTheme.colors.border.secondary,
                      boxShadow: formData.complexity === option.value
                        ? `0 8px 32px ${darkTheme.colors.accent.success}30`
                        : darkTheme.shadows.md
                    }}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-2xl">{option.icon}</span>
                      <div
                        className="font-bold text-lg"
                        style={{ color: darkTheme.colors.text.primary }}
                      >
                        {option.label}
                      </div>
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: darkTheme.colors.text.secondary }}
                    >
                      {option.desc}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Duration */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${darkTheme.colors.accent.secondary}20` }}
                >
                  <Clock
                    className="w-6 h-6"
                    style={{ color: darkTheme.colors.accent.secondary }}
                  />
                </div>
                <h2
                  className="text-2xl font-bold"
                  style={{ color: darkTheme.colors.text.primary }}
                >
                  Podcast Duration
                </h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                {[
                  { value: '5', label: '5 minutes', icon: '⚡' },
                  { value: '10', label: '10 minutes', icon: '⏱️' },
                  { value: '20', label: '20 minutes', icon: '📚' }
                ].map(option => (
                  <button
                    key={option.value}
                    onClick={() => setFormData(prev => ({ ...prev, duration: option.value }))}
                    className={`p-6 rounded-xl border-2 text-center transition-all duration-300 hover:scale-105 ${
                      formData.duration === option.value ? 'shadow-lg' : ''
                    }`}
                    style={{
                      background: formData.duration === option.value
                        ? `linear-gradient(135deg, ${darkTheme.colors.accent.secondary}20 0%, ${darkTheme.colors.accent.secondary}10 100%)`
                        : darkTheme.gradients.stockCard,
                      borderColor: formData.duration === option.value
                        ? darkTheme.colors.accent.secondary
                        : darkTheme.colors.border.secondary,
                      boxShadow: formData.duration === option.value
                        ? `0 8px 32px ${darkTheme.colors.accent.secondary}30`
                        : darkTheme.shadows.md
                    }}
                  >
                    <div className="text-3xl mb-2">{option.icon}</div>
                    <div
                      className="font-bold text-lg"
                      style={{ color: darkTheme.colors.text.primary }}
                    >
                      {option.label}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Tone */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${darkTheme.colors.accent.tertiary}20` }}
                >
                  <Volume2
                    className="w-6 h-6"
                    style={{ color: darkTheme.colors.accent.tertiary }}
                  />
                </div>
                <h2
                  className="text-2xl font-bold"
                  style={{ color: darkTheme.colors.text.primary }}
                >
                  Tone of Voice
                </h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { value: 'friendly', label: 'Friendly', desc: 'Warm and approachable', icon: '😊' },
                  { value: 'casual', label: 'Casual', desc: 'Relaxed conversation', icon: '💬' },
                  { value: 'professional', label: 'Professional', desc: 'News-style delivery', icon: '🎯' }
                ].map(option => (
                  <button
                    key={option.value}
                    onClick={() => setFormData(prev => ({ ...prev, tone: option.value }))}
                    className={`p-6 rounded-xl border-2 text-left transition-all duration-300 hover:scale-105 ${
                      formData.tone === option.value ? 'shadow-lg' : ''
                    }`}
                    style={{
                      background: formData.tone === option.value
                        ? `linear-gradient(135deg, ${darkTheme.colors.accent.tertiary}20 0%, ${darkTheme.colors.accent.tertiary}10 100%)`
                        : darkTheme.gradients.stockCard,
                      borderColor: formData.tone === option.value
                        ? darkTheme.colors.accent.tertiary
                        : darkTheme.colors.border.secondary,
                      boxShadow: formData.tone === option.value
                        ? `0 8px 32px ${darkTheme.colors.accent.tertiary}30`
                        : darkTheme.shadows.md
                    }}
                  >
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="text-2xl">{option.icon}</span>
                      <div
                        className="font-bold text-lg"
                        style={{ color: darkTheme.colors.text.primary }}
                      >
                        {option.label}
                      </div>
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: darkTheme.colors.text.secondary }}
                    >
                      {option.desc}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Delivery Time & Timezone */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div
                  className="p-2 rounded-lg"
                  style={{ backgroundColor: `${darkTheme.colors.accent.error}20` }}
                >
                  <Calendar
                    className="w-6 h-6"
                    style={{ color: darkTheme.colors.accent.error }}
                  />
                </div>
                <h2
                  className="text-2xl font-bold"
                  style={{ color: darkTheme.colors.text.primary }}
                >
                  Delivery Time & Timezone
                </h2>
              </div>
              <div className="max-w-2xl space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Time Input */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: darkTheme.colors.text.secondary }}
                    >
                      Delivery Time
                    </label>
                    <input
                      type="time"
                      value={formData.deliveryTime}
                      onChange={(e) => setFormData(prev => ({ ...prev, deliveryTime: e.target.value }))}
                      className="w-full px-4 py-3 rounded-xl border-2 text-lg font-medium transition-all duration-200 focus:outline-none"
                      style={{
                        background: darkTheme.colors.background.secondary,
                        borderColor: darkTheme.colors.border.secondary,
                        color: darkTheme.colors.text.primary,
                        boxShadow: darkTheme.shadows.md
                      }}
                    />
                  </div>

                  {/* Timezone Selector */}
                  <div>
                    <label
                      className="block text-sm font-medium mb-2"
                      style={{ color: darkTheme.colors.text.secondary }}
                    >
                      Timezone
                    </label>
                    <select
                      value={formData.timezone}
                      onChange={(e) => setFormData(prev => ({ ...prev, timezone: e.target.value }))}
                      className="w-full px-4 py-3 rounded-xl border-2 text-lg font-medium transition-all duration-200 focus:outline-none"
                      style={{
                        background: darkTheme.colors.background.secondary,
                        borderColor: darkTheme.colors.border.secondary,
                        color: darkTheme.colors.text.primary,
                        boxShadow: darkTheme.shadows.md
                      }}
                    >
                      {/* Major US Timezones */}
                      <optgroup label="🇺🇸 United States">
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="America/Anchorage">Alaska Time (AKT)</option>
                        <option value="Pacific/Honolulu">Hawaii Time (HST)</option>
                      </optgroup>

                      {/* Major International Timezones */}
                      <optgroup label="🌍 Europe & Africa">
                        <option value="Europe/London">London (GMT/BST)</option>
                        <option value="Europe/Paris">Paris (CET/CEST)</option>
                        <option value="Europe/Berlin">Berlin (CET/CEST)</option>
                        <option value="Europe/Rome">Rome (CET/CEST)</option>
                        <option value="Europe/Madrid">Madrid (CET/CEST)</option>
                        <option value="Europe/Amsterdam">Amsterdam (CET/CEST)</option>
                        <option value="Europe/Zurich">Zurich (CET/CEST)</option>
                        <option value="Europe/Stockholm">Stockholm (CET/CEST)</option>
                        <option value="Europe/Moscow">Moscow (MSK)</option>
                        <option value="Africa/Cairo">Cairo (EET)</option>
                        <option value="Africa/Johannesburg">Johannesburg (SAST)</option>
                      </optgroup>

                      {/* Asia Pacific */}
                      <optgroup label="🌏 Asia Pacific">
                        <option value="Asia/Tokyo">Tokyo (JST)</option>
                        <option value="Asia/Shanghai">Shanghai (CST)</option>
                        <option value="Asia/Hong_Kong">Hong Kong (HKT)</option>
                        <option value="Asia/Singapore">Singapore (SGT)</option>
                        <option value="Asia/Seoul">Seoul (KST)</option>
                        <option value="Asia/Kolkata">Mumbai/Delhi (IST)</option>
                        <option value="Asia/Dubai">Dubai (GST)</option>
                        <option value="Asia/Bangkok">Bangkok (ICT)</option>
                        <option value="Asia/Jakarta">Jakarta (WIB)</option>
                        <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                        <option value="Australia/Melbourne">Melbourne (AEST/AEDT)</option>
                        <option value="Australia/Perth">Perth (AWST)</option>
                        <option value="Pacific/Auckland">Auckland (NZST/NZDT)</option>
                      </optgroup>

                      {/* Americas */}
                      <optgroup label="🌎 Americas">
                        <option value="America/Toronto">Toronto (ET)</option>
                        <option value="America/Vancouver">Vancouver (PT)</option>
                        <option value="America/Mexico_City">Mexico City (CST)</option>
                        <option value="America/Sao_Paulo">São Paulo (BRT)</option>
                        <option value="America/Argentina/Buenos_Aires">Buenos Aires (ART)</option>
                        <option value="America/Lima">Lima (PET)</option>
                        <option value="America/Bogota">Bogotá (COT)</option>
                        <option value="America/Santiago">Santiago (CLT)</option>
                      </optgroup>
                    </select>
                  </div>
                </div>

                <div
                  className="p-4 rounded-lg"
                  style={{
                    background: `${darkTheme.colors.accent.primary}10`,
                    border: `1px solid ${darkTheme.colors.accent.primary}30`
                  }}
                >
                  <p
                    className="text-sm"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    🌅 Your daily podcast will be ready at <strong>{formData.deliveryTime || '09:00'}</strong> in <strong>{formData.timezone ? formData.timezone.replace(/_/g, ' ') : 'your timezone'}</strong>
                  </p>
                  <p
                    className="text-xs mt-1"
                    style={{ color: darkTheme.colors.text.tertiary }}
                  >
                    Current time in your timezone: {(() => {
                      if (!formData.timezone) return 'Loading...';

                      try {
                        // Validate timezone first
                        if (!isValidTimezone(formData.timezone)) {
                          console.warn(`Invalid timezone: ${formData.timezone}`);
                          return `Invalid timezone: ${formData.timezone}`;
                        }

                        return new Date().toLocaleTimeString('en-US', {
                          timeZone: formData.timezone,
                          hour: '2-digit',
                          minute: '2-digit',
                          hour12: true
                        });
                      } catch (error) {
                        console.error('Timezone error:', error, 'for timezone:', formData.timezone);
                        return `Error with timezone: ${formData.timezone}`;
                      }
                    })()}
                  </p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div
                className="rounded-xl border p-4"
                style={{
                  background: `${darkTheme.colors.accent.error}10`,
                  borderColor: `${darkTheme.colors.accent.error}40`
                }}
              >
                <p
                  className="text-sm font-medium"
                  style={{ color: darkTheme.colors.accent.error }}
                >
                  {error}
                </p>
              </div>
            )}

            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={isLoading || formData.selectedStocks.length === 0}
              className="w-full py-4 px-6 rounded-xl text-lg font-bold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              style={{
                background: formData.selectedStocks.length > 0
                  ? darkTheme.gradients.button
                  : darkTheme.colors.background.secondary,
                color: formData.selectedStocks.length > 0
                  ? 'white'
                  : darkTheme.colors.text.muted,
                boxShadow: formData.selectedStocks.length > 0
                  ? darkTheme.shadows.glow
                  : 'none',
                border: `2px solid ${formData.selectedStocks.length > 0
                  ? darkTheme.colors.accent.primary
                  : darkTheme.colors.border.secondary}`
              }}
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div
                    className="animate-spin rounded-full h-5 w-5 border-b-2"
                    style={{ borderColor: 'currentColor' }}
                  ></div>
                  <span>Saving...</span>
                </div>
              ) : (
                `🚀 Save Profile & Continue ${formData.selectedStocks.length > 0 ? `(${formData.selectedStocks.length} stocks selected)` : ''}`
              )}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Profile;
