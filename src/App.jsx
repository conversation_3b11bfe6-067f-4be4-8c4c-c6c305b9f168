import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from './firebase_util';
import { darkTheme, cssVariables } from './styles/theme';
import LandingPage from './LandingPage';
import Login from './Login';
import Profile from './Profile';
import Dashboard from './Dashboard';

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const [user, loading] = useAuthState(auth);

  if (loading) {
    return (
      <>
        <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
        <div
          className="min-h-screen flex items-center justify-center"
          style={{ background: darkTheme.gradients.primary }}
        >
          <div className="relative">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
            <div
              className="absolute inset-0 animate-ping rounded-full h-12 w-12 border-2 opacity-20"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
          </div>
        </div>
      </>
    );
  }

  return user ? children : <Navigate to="/login" />;
};

// Public Route Component (redirect to dashboard if already logged in)
const PublicRoute = ({ children }) => {
  const [user, loading] = useAuthState(auth);

  if (loading) {
    return (
      <>
        <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
        <div
          className="min-h-screen flex items-center justify-center"
          style={{ background: darkTheme.gradients.primary }}
        >
          <div className="relative">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
            <div
              className="absolute inset-0 animate-ping rounded-full h-12 w-12 border-2 opacity-20"
              style={{ borderColor: darkTheme.colors.accent.primary }}
            ></div>
          </div>
        </div>
      </>
    );
  }

  return user ? <Navigate to="/dashboard" /> : children;
};

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Public Routes */}
          <Route
            path="/"
            element={
              <PublicRoute>
                <LandingPage />
              </PublicRoute>
            }
          />
          <Route
            path="/login"
            element={
              <PublicRoute>
                <Login />
              </PublicRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            }
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
