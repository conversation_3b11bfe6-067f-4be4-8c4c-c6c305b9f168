/* Global styles for the sexy dark theme */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #0a0a0f;
  color: #ffffff;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a2e;
}

::-webkit-scrollbar-thumb {
  background: #00d4ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0ea5e9;
}

/* Custom scrollbar for transcript */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(26, 26, 46, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0ea5e9 0%, #6d28d9 100%);
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.6);
  }
}

/* Utility classes */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInScale {
  animation: fadeInScale 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, #1e1e2e 25%, #2a2a3e 50%, #1e1e2e 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Glass morphism effect */
.glass {
  background: rgba(30, 30, 46, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2);
}

/* Focus styles */
input:focus,
button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.3);
}

/* Selection styles */
::selection {
  background: rgba(0, 212, 255, 0.3);
  color: white;
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #1e1e2e 25%, #2a2a3e 50%, #1e1e2e 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
}

/* Custom button styles */
.btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #0ea5e9 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

/* Card styles */
.card {
  background: linear-gradient(135deg, rgba(30, 30, 46, 0.9) 0%, rgba(37, 37, 56, 0.9) 100%);
  border: 1px solid #2a2a3e;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: #4a4a7e;
  box-shadow: 0 12px 40px rgba(0, 212, 255, 0.1);
  transform: translateY(-2px);
}

/* Stock card specific styles */
.stock-card {
  position: relative;
  overflow: hidden;
}

.stock-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.stock-card:hover::before {
  left: 100%;
}

/* Responsive design helpers */
@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
  }
  
  .responsive-text {
    font-size: 0.875rem;
  }
  
  .responsive-padding {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .responsive-grid-md {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  button {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
