// Dark Theme Configuration for Sexy Stock Selection UI

export const darkTheme = {
  // Base colors
  colors: {
    // Background gradients
    background: {
      primary: '#0a0a0f',
      secondary: '#1a1a2e',
      tertiary: '#16213e',
      card: '#1e1e2e',
      cardHover: '#252538',
      glass: 'rgba(30, 30, 46, 0.8)',
      overlay: 'rgba(0, 0, 0, 0.5)'
    },
    
    // Text colors
    text: {
      primary: '#ffffff',
      secondary: '#b4b4c7',
      tertiary: '#8b8ba7',
      muted: '#6b6b87',
      accent: '#00d4ff'
    },
    
    // Accent colors
    accent: {
      primary: '#00d4ff',
      secondary: '#7c3aed',
      tertiary: '#f59e0b',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      pink: '#ec4899',
      purple: '#8b5cf6'
    },
    
    // Border colors
    border: {
      primary: '#2a2a3e',
      secondary: '#3a3a5e',
      accent: '#00d4ff',
      hover: '#4a4a7e'
    },
    
    // Stock sector colors
    sectors: {
      technology: '#00d4ff',
      finance: '#10b981',
      healthcare: '#ec4899',
      consumer: '#f59e0b',
      energy: '#ef4444',
      industrials: '#8b5cf6',
      communication: '#06b6d4',
      utilities: '#84cc16',
      materials: '#f97316',
      realEstate: '#6366f1'
    }
  },
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%)',
    secondary: 'linear-gradient(135deg, #1e1e2e 0%, #252538 100%)',
    accent: 'linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%)',
    card: 'linear-gradient(135deg, rgba(30, 30, 46, 0.9) 0%, rgba(37, 37, 56, 0.9) 100%)',
    cardHover: 'linear-gradient(135deg, rgba(37, 37, 56, 0.95) 0%, rgba(42, 42, 62, 0.95) 100%)',
    stockCard: 'linear-gradient(135deg, rgba(30, 30, 46, 0.8) 0%, rgba(26, 26, 46, 0.9) 100%)',
    stockCardHover: 'linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%)',
    button: 'linear-gradient(135deg, #00d4ff 0%, #0ea5e9 100%)',
    buttonHover: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)'
  },
  
  // Shadows
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.3)',
    glow: '0 0 20px rgba(0, 212, 255, 0.3)',
    glowHover: '0 0 30px rgba(0, 212, 255, 0.5)',
    stockCard: '0 8px 32px rgba(0, 0, 0, 0.4)',
    stockCardHover: '0 12px 40px rgba(0, 212, 255, 0.2)'
  },
  
  // Typography
  typography: {
    fontFamily: {
      primary: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      mono: '"JetBrains Mono", "Fira Code", Consolas, monospace'
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem'
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800'
    }
  },
  
  // Spacing
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem'
  },
  
  // Border radius
  borderRadius: {
    sm: '0.375rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.5rem',
    full: '9999px'
  },
  
  // Animations
  animations: {
    transition: {
      fast: '150ms ease-in-out',
      normal: '300ms ease-in-out',
      slow: '500ms ease-in-out'
    },
    keyframes: {
      fadeIn: 'fadeIn 0.3s ease-in-out',
      slideUp: 'slideUp 0.3s ease-out',
      scaleIn: 'scaleIn 0.2s ease-out',
      glow: 'glow 2s ease-in-out infinite alternate'
    }
  }
};

// CSS Custom Properties for easy theming
export const cssVariables = `
  :root {
    /* Colors */
    --bg-primary: ${darkTheme.colors.background.primary};
    --bg-secondary: ${darkTheme.colors.background.secondary};
    --bg-tertiary: ${darkTheme.colors.background.tertiary};
    --bg-card: ${darkTheme.colors.background.card};
    --bg-card-hover: ${darkTheme.colors.background.cardHover};
    
    --text-primary: ${darkTheme.colors.text.primary};
    --text-secondary: ${darkTheme.colors.text.secondary};
    --text-tertiary: ${darkTheme.colors.text.tertiary};
    --text-muted: ${darkTheme.colors.text.muted};
    --text-accent: ${darkTheme.colors.text.accent};
    
    --accent-primary: ${darkTheme.colors.accent.primary};
    --accent-secondary: ${darkTheme.colors.accent.secondary};
    --accent-success: ${darkTheme.colors.accent.success};
    --accent-warning: ${darkTheme.colors.accent.warning};
    --accent-error: ${darkTheme.colors.accent.error};
    
    --border-primary: ${darkTheme.colors.border.primary};
    --border-secondary: ${darkTheme.colors.border.secondary};
    --border-accent: ${darkTheme.colors.border.accent};
    
    /* Gradients */
    --gradient-primary: ${darkTheme.gradients.primary};
    --gradient-card: ${darkTheme.gradients.card};
    --gradient-accent: ${darkTheme.gradients.accent};
    
    /* Shadows */
    --shadow-md: ${darkTheme.shadows.md};
    --shadow-lg: ${darkTheme.shadows.lg};
    --shadow-glow: ${darkTheme.shadows.glow};
    --shadow-stock-card: ${darkTheme.shadows.stockCard};
    
    /* Typography */
    --font-primary: ${darkTheme.typography.fontFamily.primary};
    --font-mono: ${darkTheme.typography.fontFamily.mono};
    
    /* Spacing */
    --spacing-sm: ${darkTheme.spacing.sm};
    --spacing-md: ${darkTheme.spacing.md};
    --spacing-lg: ${darkTheme.spacing.lg};
    --spacing-xl: ${darkTheme.spacing.xl};
    
    /* Border Radius */
    --radius-md: ${darkTheme.borderRadius.md};
    --radius-lg: ${darkTheme.borderRadius.lg};
    --radius-xl: ${darkTheme.borderRadius.xl};
    
    /* Transitions */
    --transition-fast: ${darkTheme.animations.transition.fast};
    --transition-normal: ${darkTheme.animations.transition.normal};
  }
  
  /* Keyframe animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes scaleIn {
    from { 
      opacity: 0; 
      transform: scale(0.95); 
    }
    to { 
      opacity: 1; 
      transform: scale(1); 
    }
  }
  
  @keyframes glow {
    from { 
      box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); 
    }
    to { 
      box-shadow: 0 0 30px rgba(0, 212, 255, 0.6); 
    }
  }
`;

export default darkTheme;
