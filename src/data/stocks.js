// Comprehensive US Stocks Data
// Organized by sectors for better user experience

export const STOCK_SECTORS = {
  TECHNOLOGY: 'Technology',
  FINANCE: 'Finance',
  HEALTHCARE: 'Healthcare',
  CONSUMER: 'Consumer',
  ENERGY: 'Energy',
  INDUSTRIALS: 'Industrials',
  COMMUNICATION: 'Communication',
  UTILITIES: 'Utilities',
  MATERIALS: 'Materials',
  REAL_ESTATE: 'Real Estate'
};

export const POPULAR_STOCKS = [
  // Technology Giants
  { symbol: 'AAPL', name: 'Apple Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'MSFT', name: 'Microsoft Corporation', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'GOOGL', name: 'Alphabet Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'AMZN', name: 'Amazon.com Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'TSLA', name: 'Tesla Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'META', name: 'Meta Platforms Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'NVDA', name: 'NVIDIA Corporation', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'NFLX', name: 'Netflix Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  
  // More Technology
  { symbol: 'AMD', name: 'Advanced Micro Devices', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'INTC', name: 'Intel Corporation', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'CRM', name: 'Salesforce Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'ORCL', name: 'Oracle Corporation', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'ADBE', name: 'Adobe Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'PYPL', name: 'PayPal Holdings Inc.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'UBER', name: 'Uber Technologies Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'SPOT', name: 'Spotify Technology SA', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'ZOOM', name: 'Zoom Video Communications', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'SQ', name: 'Block Inc.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'SNAP', name: 'Snap Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'ROKU', name: 'Roku Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'SHOP', name: 'Shopify Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  { symbol: 'DOCU', name: 'DocuSign Inc.', sector: STOCK_SECTORS.TECHNOLOGY, popular: true },
  
  // Finance
  { symbol: 'JPM', name: 'JPMorgan Chase & Co.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'BAC', name: 'Bank of America Corp.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'WFC', name: 'Wells Fargo & Company', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'GS', name: 'Goldman Sachs Group Inc.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'MS', name: 'Morgan Stanley', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'V', name: 'Visa Inc.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'MA', name: 'Mastercard Inc.', sector: STOCK_SECTORS.FINANCE, popular: true },
  { symbol: 'AXP', name: 'American Express Company', sector: STOCK_SECTORS.FINANCE, popular: true },
  
  // Healthcare
  { symbol: 'JNJ', name: 'Johnson & Johnson', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'PFE', name: 'Pfizer Inc.', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'UNH', name: 'UnitedHealth Group Inc.', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'ABBV', name: 'AbbVie Inc.', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'TMO', name: 'Thermo Fisher Scientific', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'ABT', name: 'Abbott Laboratories', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'LLY', name: 'Eli Lilly and Company', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  { symbol: 'BMY', name: 'Bristol-Myers Squibb', sector: STOCK_SECTORS.HEALTHCARE, popular: true },
  
  // Consumer
  { symbol: 'WMT', name: 'Walmart Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'PG', name: 'Procter & Gamble Co.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'KO', name: 'The Coca-Cola Company', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'PEP', name: 'PepsiCo Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'NKE', name: 'NIKE Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'MCD', name: 'McDonald\'s Corporation', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'SBUX', name: 'Starbucks Corporation', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'HD', name: 'The Home Depot Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'LOW', name: 'Lowe\'s Companies Inc.', sector: STOCK_SECTORS.CONSUMER, popular: true },
  { symbol: 'TGT', name: 'Target Corporation', sector: STOCK_SECTORS.CONSUMER, popular: true },
  
  // Energy
  { symbol: 'XOM', name: 'Exxon Mobil Corporation', sector: STOCK_SECTORS.ENERGY, popular: true },
  { symbol: 'CVX', name: 'Chevron Corporation', sector: STOCK_SECTORS.ENERGY, popular: true },
  { symbol: 'COP', name: 'ConocoPhillips', sector: STOCK_SECTORS.ENERGY, popular: true },
  { symbol: 'EOG', name: 'EOG Resources Inc.', sector: STOCK_SECTORS.ENERGY, popular: true },
  { symbol: 'SLB', name: 'Schlumberger Limited', sector: STOCK_SECTORS.ENERGY, popular: true },
  
  // Communication
  { symbol: 'VZ', name: 'Verizon Communications', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'T', name: 'AT&T Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'CMCSA', name: 'Comcast Corporation', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'DIS', name: 'The Walt Disney Company', sector: STOCK_SECTORS.COMMUNICATION, popular: true },
  { symbol: 'TWTR', name: 'Twitter Inc.', sector: STOCK_SECTORS.COMMUNICATION, popular: false },
  
  // Industrials
  { symbol: 'BA', name: 'The Boeing Company', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  { symbol: 'CAT', name: 'Caterpillar Inc.', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  { symbol: 'MMM', name: '3M Company', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  { symbol: 'HON', name: 'Honeywell International', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  { symbol: 'UPS', name: 'United Parcel Service', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  { symbol: 'FDX', name: 'FedEx Corporation', sector: STOCK_SECTORS.INDUSTRIALS, popular: true },
  
  // Utilities
  { symbol: 'NEE', name: 'NextEra Energy Inc.', sector: STOCK_SECTORS.UTILITIES, popular: true },
  { symbol: 'DUK', name: 'Duke Energy Corporation', sector: STOCK_SECTORS.UTILITIES, popular: true },
  { symbol: 'SO', name: 'The Southern Company', sector: STOCK_SECTORS.UTILITIES, popular: true },
  
  // Materials
  { symbol: 'LIN', name: 'Linde plc', sector: STOCK_SECTORS.MATERIALS, popular: true },
  { symbol: 'APD', name: 'Air Products and Chemicals', sector: STOCK_SECTORS.MATERIALS, popular: true },
  
  // Real Estate
  { symbol: 'AMT', name: 'American Tower Corporation', sector: STOCK_SECTORS.REAL_ESTATE, popular: true },
  { symbol: 'PLD', name: 'Prologis Inc.', sector: STOCK_SECTORS.REAL_ESTATE, popular: true },
  { symbol: 'CCI', name: 'Crown Castle International', sector: STOCK_SECTORS.REAL_ESTATE, popular: true }
];

// Get stocks by sector
export const getStocksBySector = (sector) => {
  return POPULAR_STOCKS.filter(stock => stock.sector === sector);
};

// Get popular stocks only
export const getPopularStocks = () => {
  return POPULAR_STOCKS.filter(stock => stock.popular);
};

// Search stocks by symbol or name
export const searchStocks = (query) => {
  const searchTerm = query.toLowerCase();
  return POPULAR_STOCKS.filter(stock => 
    stock.symbol.toLowerCase().includes(searchTerm) ||
    stock.name.toLowerCase().includes(searchTerm)
  );
};

// Get all unique sectors
export const getAllSectors = () => {
  return Object.values(STOCK_SECTORS);
};

// Logo.dev API configuration with your API key
export const LOGO_DEV_CONFIG = {
  publishableKey: 'pk_VxjClnWQTISHL3N0TZ6PPw',
  secretKey: 'sk_MsRi47LmQJmB2H_9_xwBGw', // Keep this secure in production
  baseUrl: 'https://img.logo.dev/ticker/'
};

// Multiple logo sources for better reliability
export const LOGO_SOURCES = {
  // Primary source - Logo.dev (with your API key)
  logoDev: {
    baseUrl: LOGO_DEV_CONFIG.baseUrl,
    getUrl: (ticker, size) => `${LOGO_DEV_CONFIG.baseUrl}${ticker.toLowerCase()}?token=${LOGO_DEV_CONFIG.publishableKey}&size=${size}&format=png`,
    priority: 1
  },
  // Secondary source - Clearbit (free, no API key needed)
  clearbit: {
    baseUrl: 'https://logo.clearbit.com/',
    getUrl: (ticker) => {
      // Map ticker to domain for major companies
      const domainMap = {
        'AAPL': 'apple.com',
        'MSFT': 'microsoft.com',
        'GOOGL': 'google.com',
        'GOOG': 'google.com',
        'AMZN': 'amazon.com',
        'TSLA': 'tesla.com',
        'META': 'meta.com',
        'NVDA': 'nvidia.com',
        'NFLX': 'netflix.com',
        'AMD': 'amd.com',
        'INTC': 'intel.com',
        'CRM': 'salesforce.com',
        'ORCL': 'oracle.com',
        'ADBE': 'adobe.com',
        'PYPL': 'paypal.com',
        'UBER': 'uber.com',
        'SPOT': 'spotify.com',
        'JPM': 'jpmorganchase.com',
        'BAC': 'bankofamerica.com',
        'WFC': 'wellsfargo.com',
        'V': 'visa.com',
        'MA': 'mastercard.com',
        'JNJ': 'jnj.com',
        'PFE': 'pfizer.com',
        'UNH': 'unitedhealthgroup.com',
        'WMT': 'walmart.com',
        'PG': 'pg.com',
        'KO': 'coca-cola.com',
        'PEP': 'pepsi.com',
        'NKE': 'nike.com',
        'MCD': 'mcdonalds.com',
        'SBUX': 'starbucks.com',
        'HD': 'homedepot.com',
        'XOM': 'exxonmobil.com',
        'CVX': 'chevron.com',
        'VZ': 'verizon.com',
        'T': 'att.com',
        'DIS': 'disney.com',
        'BA': 'boeing.com',
        'CAT': 'caterpillar.com',
        'MMM': '3m.com'
      };
      const domain = domainMap[ticker.toUpperCase()];
      return domain ? `https://logo.clearbit.com/${domain}` : null;
    },
    priority: 2
  },
  // Tertiary source - Yahoo Finance (as fallback)
  yahoo: {
    baseUrl: 'https://logo.yahoo.com/',
    getUrl: (ticker) => `https://s.yimg.com/cv/apiv2/default/icons/${ticker.toLowerCase()}_90x90.png`,
    priority: 3
  }
};

// Generate multiple logo URLs for a stock ticker (with fallbacks)
export const getStockLogoUrls = (ticker, size = 64) => {
  const urls = [];

  // Add Logo.dev URL first (with your API key - highest quality)
  urls.push(LOGO_SOURCES.logoDev.getUrl(ticker, size));

  // Add Clearbit URL if available (good fallback)
  const clearbitUrl = LOGO_SOURCES.clearbit.getUrl(ticker);
  if (clearbitUrl) {
    urls.push(clearbitUrl);
  }

  // Add Yahoo Finance as last resort
  urls.push(LOGO_SOURCES.yahoo.getUrl(ticker));

  return urls;
};

// Get primary Logo.dev URL with API key
export const getPrimaryLogoUrl = (ticker, size = 64) => {
  return LOGO_SOURCES.logoDev.getUrl(ticker, size);
};

// Legacy function for backward compatibility
export const getStockLogoUrl = (ticker, size = 64) => {
  return getPrimaryLogoUrl(ticker, size); // Return Logo.dev URL with API key
};
