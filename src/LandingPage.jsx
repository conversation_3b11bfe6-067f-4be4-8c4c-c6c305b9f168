import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthState } from 'react-firebase-hooks/auth';
import { auth } from './firebase_util';
import { darkTheme, cssVariables } from './styles/theme';
import {
  Play,
  Pause,
  Volume2,
  TrendingUp,
  ArrowRight,
  Sparkles,
  Brain,
  Clock,
  Zap,
  BarChart3,
  Headphones,
  Star,
  CheckCircle
} from 'lucide-react';

const LandingPage = () => {
  const [user] = useAuthState(auth);
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);
  const [isDemoPlaying, setIsDemoPlaying] = useState(false);
  const demoAudioRef = useRef(null);

  useEffect(() => {
    setIsVisible(true);
    // Redirect if already logged in
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleDemoPlayPause = () => {
    if (!demoAudioRef.current) {
      demoAudioRef.current = new Audio('/podcast_c42c1e53-c718-40da-882f-d457f4e618c8.mp3');
      demoAudioRef.current.addEventListener('ended', () => {
        setIsDemoPlaying(false);
      });
    }

    if (isDemoPlaying) {
      demoAudioRef.current.pause();
      setIsDemoPlaying(false);
    } else {
      demoAudioRef.current.play();
      setIsDemoPlaying(true);
    }
  };

  // Cleanup audio when component unmounts
  useEffect(() => {
    return () => {
      if (demoAudioRef.current) {
        demoAudioRef.current.pause();
        demoAudioRef.current = null;
      }
    };
  }, []);

  const features = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: "AI-Powered Analysis",
      description: "Advanced AI analyzes market trends and delivers personalized insights tailored to your portfolio."
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "Daily Updates",
      description: "Get your personalized stock podcast delivered every morning at your preferred time."
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Lightning Fast",
      description: "Real-time market data processed instantly to keep you ahead of the curve."
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Portfolio Tracking",
      description: "Track your favorite stocks with detailed analysis and performance insights."
    }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Portfolio Manager",
      content: "LOBO AI has revolutionized my morning routine. I get all the market insights I need in just 10 minutes.",
      rating: 5
    },
    {
      name: "Michael Rodriguez",
      role: "Day Trader",
      content: "The AI-generated podcasts are incredibly accurate and help me make better trading decisions.",
      rating: 5
    },
    {
      name: "Emily Johnson",
      role: "Investment Advisor",
      content: "My clients love the personalized approach. It's like having a personal market analyst.",
      rating: 5
    }
  ];

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
      
      <div 
        className="min-h-screen"
        style={{ background: darkTheme.gradients.primary }}
      >
        {/* Navigation */}
        <nav className="relative z-50 px-4 sm:px-6 lg:px-8 py-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div 
                className="p-2 rounded-xl"
                style={{ 
                  background: darkTheme.gradients.accent,
                  boxShadow: darkTheme.shadows.glow 
                }}
              >
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <h1 
                className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
                style={{ fontFamily: darkTheme.typography.fontFamily.primary }}
              >
                LOBO AI
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/login')}
                className="px-6 py-2 rounded-xl font-medium transition-all duration-200 hover:opacity-80"
                style={{
                  background: 'transparent',
                  color: darkTheme.colors.text.secondary,
                  border: `1px solid ${darkTheme.colors.border.secondary}`
                }}
              >
                Sign In
              </button>
              <button
                onClick={() => navigate('/login')}
                className="px-6 py-2 rounded-xl font-bold transition-all duration-200 hover:scale-105"
                style={{
                  background: darkTheme.gradients.button,
                  color: 'white',
                  boxShadow: darkTheme.shadows.glow
                }}
              >
                Get Started
              </button>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="relative px-4 sm:px-6 lg:px-8 py-20">
          <div className="max-w-7xl mx-auto text-center">
            <div 
              className={`transition-all duration-1000 ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
            >
              <div className="flex items-center justify-center space-x-2 mb-6">
                <Sparkles 
                  className="w-6 h-6 animate-pulse"
                  style={{ color: darkTheme.colors.accent.primary }}
                />
                <span 
                  className="text-sm font-medium px-3 py-1 rounded-full"
                  style={{
                    background: `${darkTheme.colors.accent.primary}20`,
                    color: darkTheme.colors.accent.primary,
                    border: `1px solid ${darkTheme.colors.accent.primary}40`
                  }}
                >
                  AI-Powered Stock Analysis
                </span>
              </div>
              
              <h1 
                className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-8 leading-tight"
                style={{ 
                  background: 'linear-gradient(135deg, #ffffff 0%, #00d4ff 50%, #7c3aed 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                Your Personal
                <br />
                <span className="relative">
                  Stock Podcast
                  <div 
                    className="absolute -bottom-2 left-0 right-0 h-1 rounded-full"
                    style={{ background: darkTheme.gradients.accent }}
                  ></div>
                </span>
              </h1>
              
              <p 
                className="text-xl sm:text-2xl mb-12 max-w-3xl mx-auto leading-relaxed"
                style={{ color: darkTheme.colors.text.secondary }}
              >
                Get AI-generated, personalized stock market updates delivered as engaging podcasts. 
                Stay informed, make better decisions, and never miss a market opportunity.
              </p>
              
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
                <button
                  onClick={() => navigate('/login')}
                  className="group px-8 py-4 rounded-xl text-lg font-bold transition-all duration-300 hover:scale-105 flex items-center space-x-3"
                  style={{
                    background: darkTheme.gradients.button,
                    color: 'white',
                    boxShadow: darkTheme.shadows.glow
                  }}
                >
                  <Play className="w-6 h-6" fill="white" />
                  <span>Start Listening</span>
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </button>
                
                <button
                  onClick={handleDemoPlayPause}
                  className="px-8 py-4 rounded-xl text-lg font-medium transition-all duration-200 hover:opacity-80 flex items-center space-x-3"
                  style={{
                    background: 'transparent',
                    color: darkTheme.colors.text.primary,
                    border: `2px solid ${darkTheme.colors.border.accent}`
                  }}
                >
                  {isDemoPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Volume2 className="w-6 h-6" />
                  )}
                  <span>{isDemoPlaying ? 'Pause Demo' : 'Listen to Demo'}</span>
                </button>
              </div>
            </div>
          </div>
          
          {/* Floating Elements */}
          <div className="absolute top-20 left-10 animate-bounce">
            <div 
              className="p-3 rounded-full"
              style={{ 
                background: `${darkTheme.colors.accent.success}20`,
                border: `1px solid ${darkTheme.colors.accent.success}40`
              }}
            >
              <TrendingUp 
                className="w-6 h-6"
                style={{ color: darkTheme.colors.accent.success }}
              />
            </div>
          </div>
          
          <div className="absolute top-32 right-16 animate-pulse">
            <div 
              className="p-3 rounded-full"
              style={{ 
                background: `${darkTheme.colors.accent.tertiary}20`,
                border: `1px solid ${darkTheme.colors.accent.tertiary}40`
              }}
            >
              <Headphones 
                className="w-6 h-6"
                style={{ color: darkTheme.colors.accent.tertiary }}
              />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-4 sm:px-6 lg:px-8 py-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 
                className="text-3xl sm:text-4xl font-bold mb-6"
                style={{ color: darkTheme.colors.text.primary }}
              >
                Why Choose LOBO AI?
              </h2>
              <p 
                className="text-xl max-w-2xl mx-auto"
                style={{ color: darkTheme.colors.text.secondary }}
              >
                Experience the future of financial information with our AI-powered platform
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="p-6 rounded-2xl border transition-all duration-300 hover:scale-105 hover-lift"
                  style={{
                    background: darkTheme.gradients.card,
                    borderColor: darkTheme.colors.border.primary,
                    boxShadow: darkTheme.shadows.md
                  }}
                >
                  <div 
                    className="p-3 rounded-xl mb-4 w-fit"
                    style={{ 
                      background: `${darkTheme.colors.accent.primary}20`,
                      color: darkTheme.colors.accent.primary
                    }}
                  >
                    {feature.icon}
                  </div>
                  <h3 
                    className="text-xl font-bold mb-3"
                    style={{ color: darkTheme.colors.text.primary }}
                  >
                    {feature.title}
                  </h3>
                  <p 
                    className="text-sm leading-relaxed"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="px-4 sm:px-6 lg:px-8 py-20">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2
                className="text-3xl sm:text-4xl font-bold mb-6"
                style={{ color: darkTheme.colors.text.primary }}
              >
                Trusted by Investors
              </h2>
              <p
                className="text-xl max-w-2xl mx-auto"
                style={{ color: darkTheme.colors.text.secondary }}
              >
                Join thousands of investors who rely on LOBO AI for their daily market insights
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <div
                  key={index}
                  className="p-6 rounded-2xl border transition-all duration-300 hover:scale-105"
                  style={{
                    background: darkTheme.gradients.card,
                    borderColor: darkTheme.colors.border.primary,
                    boxShadow: darkTheme.shadows.md
                  }}
                >
                  <div className="flex items-center space-x-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 fill-current"
                        style={{ color: darkTheme.colors.accent.tertiary }}
                      />
                    ))}
                  </div>
                  <p
                    className="text-lg mb-6 leading-relaxed"
                    style={{ color: darkTheme.colors.text.primary }}
                  >
                    "{testimonial.content}"
                  </p>
                  <div>
                    <div
                      className="font-bold"
                      style={{ color: darkTheme.colors.text.primary }}
                    >
                      {testimonial.name}
                    </div>
                    <div
                      className="text-sm"
                      style={{ color: darkTheme.colors.text.tertiary }}
                    >
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="px-4 sm:px-6 lg:px-8 py-20">
          <div className="max-w-4xl mx-auto text-center">
            <div
              className="p-12 rounded-3xl border"
              style={{
                background: darkTheme.gradients.card,
                borderColor: darkTheme.colors.border.accent,
                boxShadow: darkTheme.shadows.xl
              }}
            >
              <h2
                className="text-3xl sm:text-4xl font-bold mb-6"
                style={{ color: darkTheme.colors.text.primary }}
              >
                Ready to Transform Your Investment Strategy?
              </h2>
              <p
                className="text-xl mb-8 max-w-2xl mx-auto"
                style={{ color: darkTheme.colors.text.secondary }}
              >
                Join LOBO AI today and start receiving personalized stock market insights that help you make smarter investment decisions.
              </p>

              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 mb-8">
                <div className="flex items-center space-x-2">
                  <CheckCircle
                    className="w-5 h-5"
                    style={{ color: darkTheme.colors.accent.success }}
                  />
                  <span
                    className="text-sm"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    Free 7-day trial
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle
                    className="w-5 h-5"
                    style={{ color: darkTheme.colors.accent.success }}
                  />
                  <span
                    className="text-sm"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    No credit card required
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle
                    className="w-5 h-5"
                    style={{ color: darkTheme.colors.accent.success }}
                  />
                  <span
                    className="text-sm"
                    style={{ color: darkTheme.colors.text.secondary }}
                  >
                    Cancel anytime
                  </span>
                </div>
              </div>

              <button
                onClick={() => navigate('/login')}
                className="px-12 py-4 rounded-xl text-xl font-bold transition-all duration-300 hover:scale-105"
                style={{
                  background: darkTheme.gradients.button,
                  color: 'white',
                  boxShadow: darkTheme.shadows.glow
                }}
              >
                Start Your Free Trial
              </button>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="px-4 sm:px-6 lg:px-8 py-12 border-t" style={{ borderColor: darkTheme.colors.border.primary }}>
          <div className="max-w-7xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div
                className="p-2 rounded-xl"
                style={{
                  background: darkTheme.gradients.accent,
                  boxShadow: darkTheme.shadows.glow
                }}
              >
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3
                className="text-xl font-bold"
                style={{ color: darkTheme.colors.text.primary }}
              >
                LOBO AI
              </h3>
            </div>
            <p
              className="text-sm"
              style={{ color: darkTheme.colors.text.tertiary }}
            >
              © 2025 LOBO AI. All rights reserved. Empowering investors with AI-driven insights.
            </p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default LandingPage;
