// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithPopup, 
  GoogleAuthProvider, 
  signOut,
  onAuthStateChanged 
} from 'firebase/auth';
import { 
  getDatabase, 
  ref, 
  set, 
  get, 
  child 
} from 'firebase/database';

// Your web app's Firebase configuration
// Configuration loaded from environment variables for security
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Realtime Database and get a reference to the service
export const database = getDatabase(app);

// Google Auth Provider
const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

// Authentication functions
export const signInWithGooglePopup = () => signInWithPopup(auth, googleProvider);

export const signOutUser = async () => {
  try {
    await signOut(auth);
    return { success: true };
  } catch (error) {
    console.error('Error signing out:', error);
    return { success: false, error: error.message };
  }
};

// Auth state observer
export const onAuthStateChangedListener = (callback) => 
  onAuthStateChanged(auth, callback);

// Database functions
export const saveUserProfile = async (userId, profileData) => {
  try {
    await set(ref(database, `users/${userId}/profile`), {
      ...profileData,
      updatedAt: new Date().toISOString()
    });
    return { success: true };
  } catch (error) {
    console.error('Error saving profile:', error);
    return { success: false, error: error.message };
  }
};

export const getUserProfile = async (userId) => {
  try {
    const snapshot = await get(child(ref(database), `users/${userId}/profile`));
    if (snapshot.exists()) {
      return { success: true, data: snapshot.val() };
    } else {
      return { success: true, data: null };
    }
  } catch (error) {
    console.error('Error getting profile:', error);
    return { success: false, error: error.message };
  }
};

export const savePodcastData = async (userId, podcastData) => {
  try {
    const podcastId = Date.now().toString();
    await set(ref(database, `users/${userId}/podcasts/${podcastId}`), {
      ...podcastData,
      id: podcastId,
      createdAt: new Date().toISOString()
    });
    return { success: true, podcastId };
  } catch (error) {
    console.error('Error saving podcast:', error);
    return { success: false, error: error.message };
  }
};

export const getUserPodcasts = async (userId) => {
  try {
    const snapshot = await get(child(ref(database), `users/${userId}/podcasts`));
    if (snapshot.exists()) {
      const podcasts = snapshot.val();
      // Convert object to array and sort by creation date
      const podcastArray = Object.values(podcasts).sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );
      return { success: true, data: podcastArray };
    } else {
      return { success: true, data: [] };
    }
  } catch (error) {
    console.error('Error getting podcasts:', error);
    return { success: false, error: error.message };
  }
};

// Utility function to create user document on first sign in
export const createUserDocumentFromAuth = async (userAuth) => {
  if (!userAuth) return;
  
  try {
    const userSnapshot = await get(child(ref(database), `users/${userAuth.uid}`));
    
    if (!userSnapshot.exists()) {
      const { displayName, email, uid } = userAuth;
      const createdAt = new Date().toISOString();
      
      await set(ref(database, `users/${uid}`), {
        displayName,
        email,
        createdAt,
        profile: null,
        podcasts: {}
      });
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error creating user document:', error);
    return { success: false, error: error.message };
  }
};
