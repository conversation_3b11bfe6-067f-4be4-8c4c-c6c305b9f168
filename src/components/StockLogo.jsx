import { useState, useEffect } from 'react';
import { getStockLogoUrls } from '../data/stocks';

const StockLogo = ({
  ticker,
  size = 48,
  className = '',
  showFallback = true,
  onError = null,
  onLoad = null,
  isCustomStock = false
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrlIndex, setCurrentUrlIndex] = useState(0);

  const logoUrls = getStockLogoUrls(ticker, size);
  const currentLogoUrl = logoUrls[currentUrlIndex];

  useEffect(() => {
    // If it's a custom stock, skip loading and show fallback immediately
    if (isCustomStock) {
      setImageLoaded(false);
      setImageError(true);
      setIsLoading(false);
      return;
    }

    setImageLoaded(false);
    setImageError(false);
    setIsLoading(true);
    setCurrentUrlIndex(0);
  }, [ticker, size, isCustomStock]);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
    setIsLoading(false);
    if (onLoad) onLoad();
  };

  const handleImageError = () => {
    // Try next URL if available
    if (currentUrlIndex < logoUrls.length - 1) {
      setCurrentUrlIndex(prev => prev + 1);
      setImageLoaded(false);
      setImageError(false);
      // Keep loading state true to try next URL
    } else {
      // All URLs failed, show fallback
      setImageLoaded(false);
      setImageError(true);
      setIsLoading(false);
      if (onError) onError();
    }
  };

  const FallbackLogo = () => {
    // Generate a consistent color based on ticker
    const colors = [
      'from-blue-600 to-blue-800',
      'from-green-600 to-green-800',
      'from-purple-600 to-purple-800',
      'from-red-600 to-red-800',
      'from-yellow-600 to-yellow-800',
      'from-indigo-600 to-indigo-800',
      'from-pink-600 to-pink-800',
      'from-teal-600 to-teal-800'
    ];

    const colorIndex = ticker.charCodeAt(0) % colors.length;
    const gradientColor = colors[colorIndex];

    return (
      <div
        className={`
          flex items-center justify-center
          bg-gradient-to-br ${gradientColor}
          text-white font-bold
          border border-white/20
          shadow-lg
          ${className}
        `}
        style={{
          width: size,
          height: size,
          borderRadius: '12px',
          fontSize: size < 32 ? '10px' : size < 48 ? '12px' : '14px',
          textShadow: '0 1px 2px rgba(0,0,0,0.5)'
        }}
      >
        {ticker.slice(0, size < 32 ? 2 : 3)}
      </div>
    );
  };

  const LoadingPlaceholder = () => (
    <div
      className={`
        flex items-center justify-center
        bg-gradient-to-br from-gray-700 to-gray-800
        border border-gray-600
        animate-pulse
        ${className}
      `}
      style={{
        width: size,
        height: size,
        borderRadius: '12px'
      }}
    >
      <div className="w-6 h-6 bg-gray-500 rounded-full animate-spin border-2 border-gray-400 border-t-transparent"></div>
    </div>
  );

  const CustomStockLogo = () => (
    <div
      className={`
        flex items-center justify-center
        bg-gradient-to-br from-gray-600 to-gray-800
        text-white font-bold
        border border-gray-500/30
        shadow-lg
        ${className}
      `}
      style={{
        width: size,
        height: size,
        borderRadius: '12px',
        fontSize: size < 32 ? '12px' : size < 48 ? '16px' : '20px',
        textShadow: '0 1px 2px rgba(0,0,0,0.5)'
      }}
    >
      <div className="flex items-center justify-center">
        <div>U</div>
      </div>
    </div>
  );

  // If it's a custom stock, show custom logo immediately
  if (isCustomStock) {
    return <CustomStockLogo />;
  }

  return (
    <div className="relative inline-block">
      {isLoading && <LoadingPlaceholder />}
      
      <img
        key={`${ticker}-${currentUrlIndex}`}
        src={currentLogoUrl}
        alt={`${ticker} logo`}
        className={`
          ${imageLoaded ? 'opacity-100' : 'opacity-0'}
          ${isLoading ? 'absolute inset-0' : ''}
          transition-opacity duration-300
          rounded-xl
          ${className}
        `}
        style={{
          width: size,
          height: size,
          objectFit: 'contain',
          backgroundColor: 'transparent'
        }}
        onLoad={handleImageLoad}
        onError={handleImageError}
        crossOrigin="anonymous"
      />
      
      {imageError && showFallback && !isLoading && <FallbackLogo />}
    </div>
  );
};

export default StockLogo;
