import { useState } from 'react';
import StockLogo from './StockLogo';
import { darkTheme } from '../styles/theme';

const StockCard = ({ 
  stock, 
  isSelected = false, 
  onClick, 
  size = 'medium',
  showSector = true,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const sizeConfig = {
    small: {
      logoSize: 32,
      padding: 'p-3',
      textSize: 'text-xs',
      nameSize: 'text-sm',
      height: 'h-20'
    },
    medium: {
      logoSize: 48,
      padding: 'p-4',
      textSize: 'text-sm',
      nameSize: 'text-base',
      height: 'h-24'
    },
    large: {
      logoSize: 64,
      padding: 'p-6',
      textSize: 'text-base',
      nameSize: 'text-lg',
      height: 'h-32'
    }
  };

  const config = sizeConfig[size];
  const sectorColor = darkTheme.colors.sectors[stock.sector.toLowerCase().replace(' ', '')] || darkTheme.colors.accent.primary;

  const cardStyles = {
    background: isSelected 
      ? `linear-gradient(135deg, ${sectorColor}15 0%, ${sectorColor}25 100%)`
      : isHovered
        ? darkTheme.gradients.stockCardHover
        : darkTheme.gradients.stockCard,
    borderColor: isSelected 
      ? sectorColor
      : isHovered 
        ? darkTheme.colors.border.hover
        : darkTheme.colors.border.primary,
    boxShadow: isSelected
      ? `0 8px 32px ${sectorColor}30, 0 0 0 1px ${sectorColor}50`
      : isHovered
        ? darkTheme.shadows.stockCardHover
        : darkTheme.shadows.stockCard,
    transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
  };

  return (
    <div
      className={`
        relative cursor-pointer
        border-2 rounded-xl
        transition-all duration-300 ease-out
        backdrop-blur-sm
        ${config.height}
        ${config.padding}
        ${className}
      `}
      style={cardStyles}
      onClick={() => onClick(stock)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Content */}
      <div className="flex items-center space-x-3 h-full">
        {/* Logo */}
        <div className="flex-shrink-0 relative">
          <StockLogo
            ticker={stock.symbol}
            size={config.logoSize}
            className="shadow-lg"
            isCustomStock={stock.sector === 'Custom'}
          />
          {/* Selection indicator - positioned on logo */}
          {isSelected && (
            <div
              className="absolute -top-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
              style={{ backgroundColor: sectorColor }}
            >
              <div className="absolute inset-0 rounded-full animate-ping" style={{ backgroundColor: sectorColor, opacity: 0.4 }}></div>
            </div>
          )}
        </div>

        {/* Stock Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h3
              className={`font-bold text-white ${config.nameSize} truncate flex-1`}
              style={{
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)'
              }}
            >
              {stock.symbol}
            </h3>
            {stock.popular && (
              <div className="flex-shrink-0 ml-2">
                <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                  ⭐
                </span>
              </div>
            )}
          </div>
          
          <p 
            className={`text-gray-300 ${config.textSize} truncate mb-1`}
            title={stock.name}
          >
            {stock.name}
          </p>
          
          {showSector && (
            <div className="flex items-center">
              <span 
                className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border`}
                style={{
                  backgroundColor: `${sectorColor}20`,
                  color: sectorColor,
                  borderColor: `${sectorColor}40`
                }}
              >
                {stock.sector}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Hover glow effect */}
      {isHovered && (
        <div 
          className="absolute inset-0 rounded-xl opacity-20 pointer-events-none"
          style={{
            background: `linear-gradient(135deg, ${sectorColor}30 0%, transparent 70%)`,
            filter: 'blur(1px)'
          }}
        />
      )}
    </div>
  );
};

export default StockCard;
