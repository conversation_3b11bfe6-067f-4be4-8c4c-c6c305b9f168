import { useState, useEffect } from 'react';
import Stock<PERSON>ogo from './StockLogo';
import { darkTheme } from '../styles/theme';

const CombinedStockLogo = ({
  stocks = [],
  size = 128,
  className = ''
}) => {
  const [hoveredStock, setHoveredStock] = useState(null);

  // Generate random positions for stock logos with collision avoidance
  const generateRandomPositions = (stockCount) => {
    const positions = [];
    const minDistance = 35; // Increased minimum distance for larger logos
    const margin = 20; // Increased margin from edges
    const maxAttempts = 100;

    for (let i = 0; i < stockCount; i++) {
      let attempts = 0;
      let position;
      let validPosition = false;

      while (!validPosition && attempts < maxAttempts) {
        position = {
          x: margin + Math.random() * (100 - 2 * margin),
          y: margin + Math.random() * (100 - 2 * margin),
          size: 0.7 + Math.random() * 0.4, // Random size between 0.7 and 1.1
          rotation: Math.random() * 20 - 10 // Random rotation between -10 and 10 degrees
        };

        // Check if position is valid (not too close to existing positions)
        validPosition = positions.every(existingPos => {
          const distance = Math.sqrt(
            Math.pow(position.x - existingPos.x, 2) +
            Math.pow(position.y - existingPos.y, 2)
          );
          return distance >= minDistance;
        });

        attempts++;
      }

      if (validPosition) {
        positions.push(position);
      } else {
        // Fallback: place in a safe position
        positions.push({
          x: 20 + (i * 60 / stockCount),
          y: 30 + (i % 2) * 40,
          size: 0.8,
          rotation: 0
        });
      }
    }

    return positions;
  };

  // Generate positions once when component mounts or stocks change
  const [positions, setPositions] = useState(() => generateRandomPositions(stocks.length));

  // Update positions when stocks change
  useEffect(() => {
    if (stocks.length !== positions.length) {
      setPositions(generateRandomPositions(stocks.length));
    }
  }, [stocks.length, positions.length]);

  const logoSize = size * 0.5; // Much larger individual logo size

  // No background patterns needed for random layout

  if (!stocks || stocks.length === 0) {
    return (
      <div
        className={`rounded-2xl flex items-center justify-center ${className}`}
        style={{
          width: size,
          height: size,
          background: darkTheme.gradients.accent,
          boxShadow: darkTheme.shadows.glow
        }}
      >
        <div className="text-white text-3xl font-bold">📈</div>
      </div>
    );
  }

  return (
    <div
      className={`relative rounded-2xl transition-all duration-300 overflow-hidden ${className}`}
      style={{
        width: size,
        height: size,
        background: darkTheme.gradients.card,
        boxShadow: darkTheme.shadows.glow
      }}
    >
      {/* Stock logos positioned randomly */}
      {stocks.slice(0, positions.length).map((stock, index) => {
        const position = positions[index];
        const stockLogoSize = logoSize * position.size;
        
        return (
          <div
            key={stock.symbol || stock}
            className="absolute transition-all duration-300 hover:scale-125 hover:z-10"
            style={{
              left: `${position.x}%`,
              top: `${position.y}%`,
              transform: `translate(-50%, -50%) rotate(${position.rotation}deg)`,
              zIndex: hoveredStock === index ? 10 : 1
            }}
            onMouseEnter={() => setHoveredStock(index)}
            onMouseLeave={() => setHoveredStock(null)}
          >
            <StockLogo
              ticker={typeof stock === 'string' ? stock : stock.symbol}
              size={stockLogoSize}
              className="drop-shadow-lg"
              isCustomStock={typeof stock === 'object' && stock.sector === 'Custom'}
            />
            
            {/* Tooltip on hover */}
            {hoveredStock === index && (
              <div
                className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 rounded-lg text-xs font-medium whitespace-nowrap z-20"
                style={{
                  background: darkTheme.colors.background.primary,
                  color: darkTheme.colors.text.primary,
                  border: `1px solid ${darkTheme.colors.border.primary}`,
                  boxShadow: darkTheme.shadows.md
                }}
              >
                {typeof stock === 'string' ? stock : stock.symbol}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default CombinedStockLogo;
